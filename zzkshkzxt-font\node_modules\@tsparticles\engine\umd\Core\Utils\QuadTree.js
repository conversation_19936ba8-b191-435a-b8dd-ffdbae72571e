(function (factory) {
    if (typeof module === "object" && typeof module.exports === "object") {
        var v = factory(require, exports);
        if (v !== undefined) module.exports = v;
    }
    else if (typeof define === "function" && define.amd) {
        define(["require", "exports", "./Ranges.js", "./Constants.js", "../../Utils/NumberUtils.js"], factory);
    }
})(function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.QuadTree = void 0;
    const Ranges_js_1 = require("./Ranges.js");
    const Constants_js_1 = require("./Constants.js");
    const NumberUtils_js_1 = require("../../Utils/NumberUtils.js");
    class QuadTree {
        constructor(rectangle, capacity) {
            this.rectangle = rectangle;
            this.capacity = capacity;
            this._subdivide = () => {
                const { x, y } = this.rectangle.position, { width, height } = this.rectangle.size, { capacity } = this;
                for (let i = 0; i < Constants_js_1.subdivideCount; i++) {
                    const fixedIndex = i % Constants_js_1.double;
                    this._subs.push(new QuadTree(new Ranges_js_1.Rectangle(x + width * Constants_js_1.half * fixedIndex, y + height * Constants_js_1.half * (Math.round(i * Constants_js_1.half) - fixedIndex), width * Constants_js_1.half, height * Constants_js_1.half), capacity));
                }
                this._divided = true;
            };
            this._points = [];
            this._divided = false;
            this._subs = [];
        }
        insert(point) {
            if (!this.rectangle.contains(point.position)) {
                return false;
            }
            if (this._points.length < this.capacity) {
                this._points.push(point);
                return true;
            }
            if (!this._divided) {
                this._subdivide();
            }
            return this._subs.some(sub => sub.insert(point));
        }
        query(range, check) {
            const res = [];
            if (!range.intersects(this.rectangle)) {
                return [];
            }
            for (const p of this._points) {
                if (!range.contains(p.position) &&
                    (0, NumberUtils_js_1.getDistance)(range.position, p.position) > p.particle.getRadius() &&
                    (!check || check(p.particle))) {
                    continue;
                }
                res.push(p.particle);
            }
            if (this._divided) {
                for (const sub of this._subs) {
                    res.push(...sub.query(range, check));
                }
            }
            return res;
        }
        queryCircle(position, radius, check) {
            return this.query(new Ranges_js_1.Circle(position.x, position.y, radius), check);
        }
        queryRectangle(position, size, check) {
            return this.query(new Ranges_js_1.Rectangle(position.x, position.y, size.width, size.height), check);
        }
    }
    exports.QuadTree = QuadTree;
});
