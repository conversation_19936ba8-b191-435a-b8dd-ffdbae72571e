<template>
  <div class="video-player">
    <div class="video-header">
      <h3 class="video-title">{{ videoTitle }}</h3>
      <div class="video-status">
        <span class="status-indicator" :class="statusClass"></span>
        <span class="status-text">{{ statusText }}</span>
      </div>
    </div>
    
    <div class="video-container" ref="videoContainer">
      <video 
        v-if="videoPath"
        ref="videoElement"
        :src="videoPath"
        :muted="true"
        :loop="true"
        class="video-element"
        @loadstart="onLoadStart"
        @loadeddata="onLoadedData"
        @error="onError"
        @ended="onEnded"
      ></video>
      
      <!-- 占位内容 -->
      <div v-else class="video-placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">🎬</div>
          <div class="placeholder-title">{{ placeholderTitle }}</div>
          <div class="placeholder-description">{{ placeholderDescription }}</div>
          
          <!-- 模拟设备图 -->
          <div class="equipment-diagram">
            <div class="equipment-item" :class="{ active: isEquipmentActive('capacitor') }">
              <div class="equipment-icon">⚡</div>
              <div class="equipment-label">电容组</div>
            </div>
            <div class="equipment-item" :class="{ active: isEquipmentActive('chamber') }">
              <div class="equipment-icon">🔬</div>
              <div class="equipment-label">实验腔体</div>
            </div>
            <div class="equipment-item" :class="{ active: isEquipmentActive('sensor') }">
              <div class="equipment-icon">📡</div>
              <div class="equipment-label">传感器</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="isLoading" class="loading-overlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="hasError" class="error-overlay">
        <div class="error-icon">⚠️</div>
        <div class="error-text">视频加载失败</div>
        <div class="error-description">{{ errorMessage }}</div>
      </div>
    </div>
    
    <!-- 视频信息 -->
    <div class="video-info">
      <div class="info-item">
        <span class="info-label">阶段:</span>
        <span class="info-value">{{ phaseDisplayName }}</span>
      </div>
      <div class="info-item" v-if="videoDuration">
        <span class="info-label">时长:</span>
        <span class="info-value">{{ formatDuration(videoDuration) }}</span>
      </div>
      <div class="info-item">
        <span class="info-label">状态:</span>
        <span class="info-value" :class="statusClass">{{ statusText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { EXPERIMENT_PHASES, PHASE_CONFIG } from '../config/experimentConfig.js'

// Props
const props = defineProps({
  videoPath: {
    type: String,
    default: null
  },
  isPlaying: {
    type: Boolean,
    default: false
  },
  phase: {
    type: String,
    required: true
  }
})

// 响应式数据
const videoElement = ref(null)
const videoContainer = ref(null)
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const videoDuration = ref(0)

// 计算属性
const videoTitle = computed(() => {
  return PHASE_CONFIG[props.phase]?.name || '实验视频'
})

const phaseDisplayName = computed(() => {
  return PHASE_CONFIG[props.phase]?.name || '未知阶段'
})

const placeholderTitle = computed(() => {
  switch (props.phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return '实验装备展示'
    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      return '一级充能过程'
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      return '中储充能过程'
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      return '风化充能过程'
    default:
      return '实验过程展示'
  }
})

const placeholderDescription = computed(() => {
  return PHASE_CONFIG[props.phase]?.description || '实验过程模拟展示'
})

const statusText = computed(() => {
  if (hasError.value) return '错误'
  if (isLoading.value) return '加载中'
  if (props.isPlaying) return '播放中'
  return '待机'
})

const statusClass = computed(() => {
  if (hasError.value) return 'status-error'
  if (isLoading.value) return 'status-loading'
  if (props.isPlaying) return 'status-playing'
  return 'status-idle'
})

// 方法
const isEquipmentActive = (equipment) => {
  // 根据当前阶段判断设备是否激活
  switch (props.phase) {
    case EXPERIMENT_PHASES.PREPARATION:
      return false
    case EXPERIMENT_PHASES.PRIMARY_CHARGING:
      return equipment === 'capacitor'
    case EXPERIMENT_PHASES.INTERMEDIATE_CHARGING:
      return equipment === 'capacitor' || equipment === 'chamber'
    case EXPERIMENT_PHASES.FINAL_CHARGING:
      return true
    default:
      return false
  }
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 视频事件处理
const onLoadStart = () => {
  isLoading.value = true
  hasError.value = false
}

const onLoadedData = () => {
  isLoading.value = false
  if (videoElement.value) {
    videoDuration.value = videoElement.value.duration
  }
}

const onError = (event) => {
  isLoading.value = false
  hasError.value = true
  errorMessage.value = '无法加载视频文件'
  console.error('Video error:', event)
}

const onEnded = () => {
  // 视频结束处理
}

// 控制视频播放
const playVideo = () => {
  if (videoElement.value && !hasError.value) {
    videoElement.value.play().catch(err => {
      console.error('Play error:', err)
    })
  }
}

const pauseVideo = () => {
  if (videoElement.value) {
    videoElement.value.pause()
  }
}

// 监听播放状态变化
watch(() => props.isPlaying, (newVal) => {
  if (newVal) {
    playVideo()
  } else {
    pauseVideo()
  }
})

// 监听视频路径变化
watch(() => props.videoPath, () => {
  hasError.value = false
  errorMessage.value = ''
  videoDuration.value = 0
})

// 生命周期
onMounted(() => {
  // 初始化
})

onUnmounted(() => {
  if (videoElement.value) {
    videoElement.value.pause()
  }
})
</script>

<style scoped>
.video-player {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(10, 25, 46, 0.8);
  border: 1px solid #00F0FF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 240, 255, 0.2);
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: rgba(0, 240, 255, 0.1);
  border-bottom: 1px solid rgba(0, 240, 255, 0.3);
}

.video-title {
  font-size: 16px;
  font-weight: bold;
  color: #00F0FF;
  margin: 0;
}

.video-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
}

.status-indicator.status-playing {
  background: #39FF14;
  box-shadow: 0 0 10px #39FF14;
  animation: pulse 1s ease-in-out infinite alternate;
}

.status-indicator.status-loading {
  background: #FFC400;
  animation: blink 1s ease-in-out infinite;
}

.status-indicator.status-error {
  background: #FF4444;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.status-text {
  font-size: 12px;
  color: #ccc;
}

.video-container {
  flex: 1;
  position: relative;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, rgba(10, 25, 46, 0.9), rgba(17, 34, 64, 0.9));
}

.placeholder-content {
  text-align: center;
  color: #00F0FF;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 15px;
}

.placeholder-title {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #00F0FF;
}

.placeholder-description {
  font-size: 14px;
  color: #39FF14;
  margin-bottom: 30px;
}

.equipment-diagram {
  display: flex;
  justify-content: center;
  gap: 30px;
  margin-top: 20px;
}

.equipment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 15px;
  border: 1px solid rgba(0, 240, 255, 0.3);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.equipment-item.active {
  border-color: #39FF14;
  background: rgba(57, 255, 20, 0.1);
  box-shadow: 0 0 15px rgba(57, 255, 20, 0.3);
}

.equipment-icon {
  font-size: 24px;
}

.equipment-label {
  font-size: 12px;
  color: #ccc;
}

.equipment-item.active .equipment-label {
  color: #39FF14;
  font-weight: bold;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(0, 240, 255, 0.3);
  border-top: 3px solid #00F0FF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text,
.error-text {
  font-size: 16px;
  margin-bottom: 5px;
}

.error-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.error-description {
  font-size: 12px;
  color: #ccc;
}

.video-info {
  display: flex;
  justify-content: space-between;
  padding: 10px 20px;
  background: rgba(0, 240, 255, 0.05);
  border-top: 1px solid rgba(0, 240, 255, 0.3);
}

.info-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.info-label {
  font-size: 11px;
  color: #ccc;
}

.info-value {
  font-size: 11px;
  font-weight: bold;
  color: #18FFFF;
}

.info-value.status-playing {
  color: #39FF14;
}

.info-value.status-error {
  color: #FF4444;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .equipment-diagram {
    flex-direction: column;
    gap: 15px;
  }
  
  .video-info {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
