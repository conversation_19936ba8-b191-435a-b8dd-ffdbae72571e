import { MoveDirection, type MoveDirectionAlt } from "../../../../Enums/Directions/MoveDirection.js";
import type { IDistance } from "../../../../Core/Interfaces/IDistance.js";
import type { IMove } from "../../../Interfaces/Particles/Move/IMove.js";
import type { IOptionLoader } from "../../../Interfaces/IOptionLoader.js";
import { MoveAngle } from "./MoveAngle.js";
import { MoveAttract } from "./MoveAttract.js";
import { MoveCenter } from "./MoveCenter.js";
import { MoveGravity } from "./MoveGravity.js";
import { MovePath } from "./Path/MovePath.js";
import { MoveTrail } from "./MoveTrail.js";
import { OutModes } from "./OutModes.js";
import type { RangeValue } from "../../../../Types/RangeValue.js";
import type { RecursivePartial } from "../../../../Types/RecursivePartial.js";
import { Spin } from "./Spin.js";
export declare class Move implements IMove, IOptionLoader<IMove> {
    readonly angle: MoveAngle;
    readonly attract: MoveAttract;
    readonly center: MoveCenter;
    decay: RangeValue;
    direction: MoveDirection | keyof typeof MoveDirection | MoveDirectionAlt | number;
    distance: Partial<IDistance>;
    drift: RangeValue;
    enable: boolean;
    readonly gravity: MoveGravity;
    readonly outModes: OutModes;
    readonly path: MovePath;
    random: boolean;
    size: boolean;
    speed: RangeValue;
    readonly spin: Spin;
    straight: boolean;
    readonly trail: MoveTrail;
    vibrate: boolean;
    warp: boolean;
    constructor();
    load(data?: RecursivePartial<IMove>): void;
}
