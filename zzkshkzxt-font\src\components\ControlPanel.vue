<template>
  <div class="control-panel">
    <div class="control-left">
      <!-- 阶段指示器 -->
      <div class="phase-indicators">
        <div 
          v-for="(phase, key) in PHASE_CONFIG" 
          :key="key"
          class="phase-indicator"
          :class="{ 
            active: currentPhase === key,
            completed: isPhaseCompleted(key)
          }"
          @click="$emit('phase-change', key)"
        >
          <div class="phase-dot"></div>
          <span class="phase-name">{{ phase.name }}</span>
        </div>
      </div>
    </div>
    
    <div class="control-center">
      <!-- 主控制按钮 -->
      <div class="main-controls">
        <button 
          @click="$emit('play')" 
          v-if="!isPlaying"
          class="control-btn play-btn"
        >
          <span class="btn-icon">▶</span>
          <span class="btn-text">开始</span>
        </button>
        
        <button 
          @click="$emit('pause')" 
          v-if="isPlaying"
          class="control-btn pause-btn"
        >
          <span class="btn-icon">⏸</span>
          <span class="btn-text">暂停</span>
        </button>
        
        <button 
          @click="$emit('reset')"
          class="control-btn reset-btn"
        >
          <span class="btn-icon">⏹</span>
          <span class="btn-text">重置</span>
        </button>
      </div>
      
      <!-- 进度条 -->
      <div class="progress-container">
        <div class="progress-bar">
          <div 
            class="progress-fill"
            :style="{ width: `${progress}%` }"
          ></div>
        </div>
        <div class="progress-text">{{ progress.toFixed(1) }}%</div>
      </div>
    </div>
    
    <div class="control-right">
      <!-- 当前阶段信息 -->
      <div class="current-phase-info">
        <div class="phase-title">{{ currentPhaseConfig?.name }}</div>
        <div class="phase-description">{{ currentPhaseConfig?.description }}</div>
        <div class="phase-duration" v-if="currentPhaseConfig?.duration">
          持续时间: {{ formatDuration(currentPhaseConfig.duration) }}
        </div>
      </div>
      
      <!-- 系统状态 -->
      <div class="system-status">
        <div class="status-item">
          <span class="status-label">系统状态:</span>
          <span class="status-value" :class="systemStatusClass">
            {{ systemStatusText }}
          </span>
        </div>
        <div class="status-item">
          <span class="status-label">运行时间:</span>
          <span class="status-value">{{ formatRuntime(runtime) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { EXPERIMENT_PHASES, PHASE_CONFIG } from '../config/experimentConfig.js'

// Props
const props = defineProps({
  currentPhase: {
    type: String,
    required: true
  },
  isPlaying: {
    type: Boolean,
    default: false
  },
  progress: {
    type: Number,
    default: 0
  }
})

// Emits
const emit = defineEmits(['play', 'pause', 'reset', 'phase-change'])

// 响应式数据
const runtime = ref(0)
let runtimeTimer = null

// 计算属性
const currentPhaseConfig = computed(() => {
  return PHASE_CONFIG[props.currentPhase]
})

const systemStatusText = computed(() => {
  if (props.isPlaying) return '运行中'
  if (props.progress > 0) return '已暂停'
  return '待机'
})

const systemStatusClass = computed(() => {
  if (props.isPlaying) return 'status-running'
  if (props.progress > 0) return 'status-paused'
  return 'status-idle'
})

// 方法
const isPhaseCompleted = (phaseKey) => {
  const phases = Object.keys(PHASE_CONFIG)
  const currentIndex = phases.indexOf(props.currentPhase)
  const phaseIndex = phases.indexOf(phaseKey)
  return phaseIndex < currentIndex || (phaseIndex === currentIndex && props.progress >= 100)
}

const formatDuration = (ms) => {
  const seconds = ms / 1000
  return `${seconds}秒`
}

const formatRuntime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 运行时间计时器
const startRuntimeTimer = () => {
  runtimeTimer = setInterval(() => {
    if (props.isPlaying) {
      runtime.value++
    }
  }, 1000)
}

const stopRuntimeTimer = () => {
  if (runtimeTimer) {
    clearInterval(runtimeTimer)
    runtimeTimer = null
  }
}

// 生命周期
onMounted(() => {
  startRuntimeTimer()
})

onUnmounted(() => {
  stopRuntimeTimer()
})

// 监听播放状态变化
const handlePlayStateChange = () => {
  if (!props.isPlaying && props.progress === 0) {
    runtime.value = 0
  }
}

// 暴露给父组件
defineExpose({
  handlePlayStateChange
})
</script>

<style scoped>
.control-panel {
  display: flex;
  align-items: center;
  background: rgba(10, 25, 46, 0.9);
  border: 1px solid #00F0FF;
  border-radius: 8px;
  padding: 6px 12px;
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.3);
  backdrop-filter: blur(15px);
  height: 50px;
}

.control-left {
  flex: 2;
}

.control-center {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.control-right {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 阶段指示器 */
.phase-indicators {
  display: flex;
  gap: 12px;
  align-items: center;
}

.phase-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 6px;
}

.phase-indicator:hover {
  background: rgba(0, 240, 255, 0.1);
}

.phase-indicator.active {
  background: rgba(0, 240, 255, 0.2);
}

.phase-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
  transition: all 0.3s ease;
}

.phase-indicator.active .phase-dot {
  background: #00F0FF;
  box-shadow: 0 0 10px #00F0FF;
}

.phase-indicator.completed .phase-dot {
  background: #39FF14;
  box-shadow: 0 0 10px #39FF14;
}

.phase-name {
  font-size: 10px;
  color: #ccc;
  white-space: nowrap;
}

.phase-indicator.active .phase-name {
  color: #00F0FF;
  font-weight: bold;
}

.phase-indicator.completed .phase-name {
  color: #39FF14;
}

/* 主控制按钮 */
.main-controls {
  display: flex;
  gap: 8px;
}

.control-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.play-btn {
  background: linear-gradient(45deg, #39FF14, #18FFFF);
  color: #0A192E;
}

.pause-btn {
  background: linear-gradient(45deg, #FFC400, #FF6B35);
  color: #0A192E;
}

.reset-btn {
  background: linear-gradient(45deg, #7C4DFF, #E91E63);
  color: white;
}

.control-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.btn-icon {
  font-size: 16px;
}

/* 进度条 */
.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
  width: 100%;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #00F0FF, #39FF14);
  transition: width 0.3s ease;
  box-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
}

.progress-text {
  font-size: 12px;
  color: #00F0FF;
  font-weight: bold;
  min-width: 50px;
  text-align: right;
}

/* 当前阶段信息 */
.current-phase-info {
  text-align: right;
}

.phase-title {
  font-size: 14px;
  font-weight: bold;
  color: #00F0FF;
  margin-bottom: 2px;
}

.phase-description {
  font-size: 10px;
  color: #39FF14;
  margin-bottom: 2px;
}

.phase-duration {
  font-size: 9px;
  color: #ccc;
}

/* 系统状态 */
.system-status {
  text-align: right;
}

.status-item {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-bottom: 5px;
}

.status-label {
  font-size: 12px;
  color: #ccc;
}

.status-value {
  font-size: 12px;
  font-weight: bold;
}

.status-running {
  color: #39FF14;
}

.status-paused {
  color: #FFC400;
}

.status-idle {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .control-panel {
    flex-direction: column;
    gap: 20px;
  }
  
  .control-left,
  .control-center,
  .control-right {
    width: 100%;
  }
  
  .phase-indicators {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .current-phase-info,
  .system-status {
    text-align: center;
  }
}
</style>
