<template>
  <div class="experiment-dashboard">
    <!-- 控制面板 -->
    <ControlPanel 
      :current-phase="currentPhase"
      :is-playing="isPlaying"
      :progress="progress"
      @play="startExperiment"
      @pause="pauseExperiment"
      @reset="resetExperiment"
      @phase-change="changePhase"
    />
    
    <!-- 主要布局区域 -->
    <div class="dashboard-grid">
      <!-- 左侧栏 -->
      <div class="left-column">
        <div class="grid-item">
          <WaveformChart
            title="一级储能电容组电压"
            :data="voltageData.primary"
            :color="colors.primary"
            unit="V"
            :max-value="1000000"
          />
        </div>
        <div class="grid-item">
          <WaveformChart
            title="中储电容组电压"
            :data="voltageData.intermediate"
            :color="colors.secondary"
            unit="V"
            :max-value="30000000"
          />
        </div>
        <div class="grid-item">
          <WaveformChart
            title="风化电容组电压"
            :data="voltageData.final"
            :color="colors.accent"
            unit="V"
            :max-value="100000000"
          />
        </div>
      </div>

      <!-- 中间栏 -->
      <div class="center-column">
        <div class="center-top grid-item">
          <VideoPlayer
            :video-path="currentVideoPath"
            :is-playing="isPlaying"
            :phase="currentPhase"
          />
        </div>
        <div class="center-bottom">
          <div class="center-bottom-left grid-item">
            <PressureDisplay
              :pressure-data="pressureData"
              :gas-data="gasData"
            />
          </div>
          <div class="center-bottom-right grid-item">
            <PulseWaveform
              :pulse-data="pulseData"
              :phase="currentPhase"
            />
          </div>
        </div>
      </div>

      <!-- 右侧栏 -->
      <div class="right-column">
        <div class="grid-item">
          <WaveformChart
            title="一级储能电容组电流"
            :data="currentData.primary"
            :color="colors.primary"
            unit="A"
            :max-value="10000"
          />
        </div>
        <div class="grid-item">
          <WaveformChart
            title="中储电容组电流"
            :data="currentData.intermediate"
            :color="colors.secondary"
            unit="A"
            :max-value="10000"
          />
        </div>
        <div class="grid-item">
          <WaveformChart
            title="风化电容组电流"
            :data="currentData.final"
            :color="colors.accent"
            unit="A"
            :max-value="50000"
          />
        </div>
      </div>
    </div>
    
    <!-- 电场建立阶段的特殊布局 -->
    <div v-if="currentPhase === 'field_establishment'" class="field-establishment-layout">
      <div class="field-left">
        <FieldStrengthDisplay :field-data="fieldStrengthData" />
      </div>
      <div class="field-right">
        <ReportSummary :report-data="reportSummary" />
      </div>
      <div class="field-bottom">
        <MarxWaveform :marx-data="marxData" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { EXPERIMENT_PHASES, PHASE_CONFIG, COLORS } from '../config/experimentConfig.js'
import { 
  generateVoltageData, 
  generateCurrentData, 
  generatePressureData,
  generatePulseData,
  generateFieldStrengthData,
  generateMarxWaveform,
  generateReportSummary
} from '../data/mockData.js'

// 导入子组件
import ControlPanel from './ControlPanel.vue'
import WaveformChart from './WaveformChart.vue'
import VideoPlayer from './VideoPlayer.vue'
import PressureDisplay from './PressureDisplay.vue'
import PulseWaveform from './PulseWaveform.vue'
import FieldStrengthDisplay from './FieldStrengthDisplay.vue'
import ReportSummary from './ReportSummary.vue'
import MarxWaveform from './MarxWaveform.vue'

// 响应式状态
const currentPhase = ref(EXPERIMENT_PHASES.PREPARATION)
const isPlaying = ref(false)
const progress = ref(0)
const colors = COLORS

// 数据状态
const voltageData = reactive({
  primary: [],
  intermediate: [],
  final: []
})

const currentData = reactive({
  primary: [],
  intermediate: [],
  final: []
})

const pressureData = ref([])
const gasData = ref({ type: 'SF4', pressure: 0 })
const pulseData = ref([])
const fieldStrengthData = ref([])
const marxData = ref([])
const reportSummary = ref({})

// 计算属性
const currentVideoPath = computed(() => {
  return PHASE_CONFIG[currentPhase.value]?.videoPath || null
})

// 定时器
let phaseTimer = null
let dataUpdateTimer = null

// 实验控制方法
const startExperiment = () => {
  isPlaying.value = true
  updatePhaseData()
  startPhaseTimer()
}

const pauseExperiment = () => {
  isPlaying.value = false
  clearTimers()
}

const resetExperiment = () => {
  isPlaying.value = false
  currentPhase.value = EXPERIMENT_PHASES.PREPARATION
  progress.value = 0
  clearTimers()
  updatePhaseData()
}

const changePhase = (phase) => {
  currentPhase.value = phase
  progress.value = 0
  updatePhaseData()
  if (isPlaying.value) {
    startPhaseTimer()
  }
}

// 更新阶段数据
const updatePhaseData = () => {
  const phase = currentPhase.value
  
  // 更新电压数据
  voltageData.primary = generateVoltageData('PRIMARY', phase)
  voltageData.intermediate = generateVoltageData('INTERMEDIATE', phase)
  voltageData.final = generateVoltageData('FINAL', phase)
  
  // 更新电流数据
  currentData.primary = generateCurrentData('PRIMARY', phase)
  currentData.intermediate = generateCurrentData('INTERMEDIATE', phase)
  currentData.final = generateCurrentData('FINAL', phase)
  
  // 更新其他数据
  pressureData.value = generatePressureData(phase)
  pulseData.value = generatePulseData(phase)
  
  if (phase === EXPERIMENT_PHASES.FIELD_ESTABLISHMENT) {
    fieldStrengthData.value = generateFieldStrengthData()
    marxData.value = generateMarxWaveform()
    reportSummary.value = generateReportSummary()
  }
}

// 启动阶段计时器
const startPhaseTimer = () => {
  clearTimers()
  
  const phaseDuration = PHASE_CONFIG[currentPhase.value]?.duration
  if (!phaseDuration) return
  
  const startTime = Date.now()
  
  const updateProgress = () => {
    const elapsed = Date.now() - startTime
    progress.value = Math.min((elapsed / phaseDuration) * 100, 100)
    
    if (progress.value >= 100) {
      // 自动进入下一阶段
      const phases = Object.values(EXPERIMENT_PHASES)
      const currentIndex = phases.indexOf(currentPhase.value)
      if (currentIndex < phases.length - 1) {
        currentPhase.value = phases[currentIndex + 1]
        progress.value = 0
        updatePhaseData()
        startPhaseTimer()
      } else {
        isPlaying.value = false
      }
    } else {
      phaseTimer = requestAnimationFrame(updateProgress)
    }
  }
  
  phaseTimer = requestAnimationFrame(updateProgress)
}

// 清除定时器
const clearTimers = () => {
  if (phaseTimer) {
    cancelAnimationFrame(phaseTimer)
    phaseTimer = null
  }
  if (dataUpdateTimer) {
    clearInterval(dataUpdateTimer)
    dataUpdateTimer = null
  }
}

// 生命周期
onMounted(() => {
  updatePhaseData()
})

onUnmounted(() => {
  clearTimers()
})
</script>

<style scoped>
.experiment-dashboard {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 8px;
  gap: 8px;
  height: calc(100vh - 60px);
  overflow: hidden;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 8px;
  flex: 1;
  min-height: 0;
}

.left-column,
.right-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 0;
}

.center-column {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-height: 0;
}

.center-top {
  flex: 2;
}

.center-bottom {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  min-height: 0;
}

.grid-item {
  background: rgba(10, 25, 46, 0.8);
  border: 1px solid #00F0FF;
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.grid-item:hover {
  border-color: #39FF14;
  box-shadow: 0 0 30px rgba(57, 255, 20, 0.3);
  transform: translateY(-2px);
}

.field-establishment-layout {
  position: fixed;
  top: 80px;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(10, 25, 46, 0.95);
  z-index: 100;
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 20px;
  padding: 20px;
}

.field-left {
  grid-row: 1 / 2;
  grid-column: 1 / 2;
}

.field-right {
  grid-row: 1 / 2;
  grid-column: 2 / 2;
}

.field-bottom {
  grid-row: 2 / 3;
  grid-column: 1 / 3;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
  
  .center-bottom {
    grid-template-columns: 1fr;
  }
}
</style>
