<template>
  <div class="pressure-display">
    <div class="display-header">
      <h3 class="display-title">腔体气压监测</h3>
      <div class="gas-type-indicator">
        <span class="gas-label">气体类型:</span>
        <span class="gas-value" :style="{ color: gasTypeColor }">{{ gasData.type }}</span>
      </div>
    </div>
    
    <div class="pressure-content">
      <!-- 数字显示 -->
      <div class="pressure-digital">
        <div class="digital-value">{{ formattedPressure }}</div>
        <div class="digital-unit">{{ pressureUnit }}</div>
      </div>
      
      <!-- 压力表盘 -->
      <div class="pressure-gauge" ref="gaugeContainer">
        <div class="gauge-background">
          <div class="gauge-scale">
            <div v-for="i in 10" :key="i" class="scale-mark" :style="getScaleStyle(i)"></div>
          </div>
          <div class="gauge-needle" :style="needleStyle"></div>
          <div class="gauge-center"></div>
        </div>
        <div class="gauge-labels">
          <span class="gauge-min">0</span>
          <span class="gauge-max">{{ maxPressure / 1000000 }}MPa</span>
        </div>
      </div>
      
      <!-- 气体流动动画 -->
      <div class="gas-flow" v-if="isFlowing">
        <div v-for="i in 6" :key="i" class="flow-particle" :style="getParticleStyle(i)"></div>
      </div>
    </div>
    
    <!-- 压力波形图 -->
    <div class="pressure-chart" ref="chartContainer"></div>
    
    <!-- 状态信息 -->
    <div class="pressure-info">
      <div class="info-row">
        <span class="info-label">当前压力:</span>
        <span class="info-value">{{ formattedPressure }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">目标压力:</span>
        <span class="info-value">{{ formattedTargetPressure }}</span>
      </div>
      <div class="info-row">
        <span class="info-label">充气状态:</span>
        <span class="info-value" :class="flowStatusClass">{{ flowStatusText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { formatPressure } from '../utils/formatters.js'
import { GAS_TYPES } from '../config/experimentConfig.js'

// Props
const props = defineProps({
  pressureData: {
    type: Array,
    default: () => []
  },
  gasData: {
    type: Object,
    default: () => ({ type: 'SF4', pressure: 0 })
  }
})

// 响应式数据
const gaugeContainer = ref(null)
const chartContainer = ref(null)
const maxPressure = 2500000 // 2.5 MPa
let chartInstance = null

// 计算属性
const currentPressure = computed(() => {
  if (!props.pressureData || props.pressureData.length === 0) return 0
  return parseFloat(props.pressureData[props.pressureData.length - 1]?.value || 0)
})

const formattedPressure = computed(() => {
  return formatPressure(currentPressure.value)
})

const formattedTargetPressure = computed(() => {
  return formatPressure(2000000) // 目标2MPa
})

const pressureUnit = computed(() => {
  if (currentPressure.value >= 1000000) return 'MPa'
  if (currentPressure.value >= 1000) return 'KPa'
  return 'Pa'
})

const gasTypeColor = computed(() => {
  return GAS_TYPES[props.gasData.type]?.color || '#00F0FF'
})

const isFlowing = computed(() => {
  return currentPressure.value > 100000 // 大于100KPa时显示流动
})

const flowStatusText = computed(() => {
  if (currentPressure.value < 100000) return '未充气'
  if (currentPressure.value < 1800000) return '充气中'
  return '已充满'
})

const flowStatusClass = computed(() => {
  if (currentPressure.value < 100000) return 'status-idle'
  if (currentPressure.value < 1800000) return 'status-flowing'
  return 'status-full'
})

const needleStyle = computed(() => {
  const percentage = Math.min(currentPressure.value / maxPressure, 1)
  const angle = -90 + (percentage * 180) // -90度到90度
  return {
    transform: `rotate(${angle}deg)`,
    transformOrigin: 'bottom center'
  }
})

// 方法
const getScaleStyle = (index) => {
  const angle = -90 + ((index - 1) / 9) * 180
  return {
    transform: `rotate(${angle}deg)`,
    transformOrigin: 'bottom center'
  }
}

const getParticleStyle = (index) => {
  const delay = index * 0.2
  const size = 3 + Math.random() * 3
  return {
    animationDelay: `${delay}s`,
    width: `${size}px`,
    height: `${size}px`,
    left: `${20 + Math.random() * 60}%`,
    animationDuration: `${2 + Math.random()}s`
  }
}

// 初始化压力图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '5%',
      right: '5%',
      top: '10%',
      bottom: '10%'
    },
    xAxis: {
      type: 'value',
      name: '时间(s)',
      nameTextStyle: { color: '#00F0FF', fontSize: 10 },
      axisLine: { lineStyle: { color: '#00F0FF60' } },
      axisTick: { lineStyle: { color: '#00F0FF60' } },
      axisLabel: { color: '#00F0FFCC', fontSize: 9 },
      splitLine: { lineStyle: { color: '#00F0FF20' } }
    },
    yAxis: {
      type: 'value',
      name: 'Pa',
      nameTextStyle: { color: '#00F0FF', fontSize: 10 },
      axisLine: { lineStyle: { color: '#00F0FF60' } },
      axisTick: { lineStyle: { color: '#00F0FF60' } },
      axisLabel: { 
        color: '#00F0FFCC', 
        fontSize: 9,
        formatter: (value) => {
          if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
          if (value >= 1000) return `${(value / 1000).toFixed(0)}K`
          return value.toFixed(0)
        }
      },
      splitLine: { lineStyle: { color: '#00F0FF20' } }
    },
    series: [{
      type: 'line',
      data: [],
      smooth: true,
      lineStyle: {
        color: '#39FF14',
        width: 2,
        shadowColor: '#39FF14',
        shadowBlur: 8
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [
            { offset: 0, color: '#39FF1440' },
            { offset: 1, color: '#39FF1410' }
          ]
        }
      },
      symbol: 'none',
      animation: true
    }],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(10, 25, 46, 0.9)',
      borderColor: '#39FF14',
      textStyle: { color: '#fff' },
      formatter: (params) => {
        const point = params[0]
        return `
          <div style="padding: 5px;">
            <div style="color: #39FF14; font-weight: bold;">腔体气压</div>
            <div>时间: ${point.data[0]}s</div>
            <div>压力: ${formatPressure(point.data[1])}</div>
          </div>
        `
      }
    }
  }
  
  chartInstance.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance || !props.pressureData) return
  
  const chartData = props.pressureData.map(item => [
    parseFloat(item.time),
    parseFloat(item.value)
  ])
  
  chartInstance.setOption({
    series: [{ data: chartData }]
  })
}

// 监听数据变化
watch(() => props.pressureData, () => {
  updateChart()
}, { deep: true })

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
    updateChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.pressure-display {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(10, 25, 46, 0.8);
  border: 1px solid #39FF14;
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 0 20px rgba(57, 255, 20, 0.2);
}

.display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(57, 255, 20, 0.3);
}

.display-title {
  font-size: 14px;
  font-weight: bold;
  color: #39FF14;
  margin: 0;
}

.gas-type-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.gas-label {
  font-size: 11px;
  color: #ccc;
}

.gas-value {
  font-size: 12px;
  font-weight: bold;
}

.pressure-content {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 15px;
  position: relative;
}

.pressure-digital {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.digital-value {
  font-size: 24px;
  font-weight: bold;
  color: #39FF14;
  text-shadow: 0 0 10px rgba(57, 255, 20, 0.5);
  font-family: 'Courier New', monospace;
}

.digital-unit {
  font-size: 12px;
  color: #ccc;
  margin-top: 5px;
}

.pressure-gauge {
  position: relative;
  width: 80px;
  height: 40px;
}

.gauge-background {
  position: relative;
  width: 100%;
  height: 100%;
  border: 2px solid #39FF14;
  border-bottom: none;
  border-radius: 80px 80px 0 0;
  overflow: hidden;
}

.gauge-scale {
  position: absolute;
  width: 100%;
  height: 100%;
}

.scale-mark {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 1px;
  height: 8px;
  background: #39FF14;
  transform-origin: bottom center;
}

.gauge-needle {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 2px;
  height: 30px;
  background: #FFC400;
  transform-origin: bottom center;
  transition: transform 0.5s ease;
  box-shadow: 0 0 5px #FFC400;
}

.gauge-center {
  position: absolute;
  bottom: -3px;
  left: 50%;
  width: 6px;
  height: 6px;
  background: #FFC400;
  border-radius: 50%;
  transform: translateX(-50%);
  box-shadow: 0 0 8px #FFC400;
}

.gauge-labels {
  display: flex;
  justify-content: space-between;
  margin-top: 5px;
  font-size: 9px;
  color: #ccc;
}

.gas-flow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.flow-particle {
  position: absolute;
  background: #18FFFF;
  border-radius: 50%;
  animation: flowUp 3s linear infinite;
  box-shadow: 0 0 5px #18FFFF;
}

@keyframes flowUp {
  0% {
    bottom: 0;
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    bottom: 100%;
    opacity: 0;
  }
}

.pressure-chart {
  flex: 1;
  min-height: 100px;
  margin-bottom: 10px;
}

.pressure-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 10px;
  color: #ccc;
}

.info-value {
  font-size: 11px;
  font-weight: bold;
  color: #18FFFF;
}

.info-value.status-idle {
  color: #666;
}

.info-value.status-flowing {
  color: #FFC400;
}

.info-value.status-full {
  color: #39FF14;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pressure-content {
    flex-direction: column;
    gap: 10px;
  }
  
  .display-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}
</style>
