<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import ExperimentDashboard from './components/ExperimentDashboard.vue'
import ParticleBackground from './components/ParticleBackground.vue'

// 响应式状态
const isFullscreen = ref(false)

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isFullscreen.value = true
  } else {
    document.exitFullscreen()
    isFullscreen.value = false
  }
}

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
}

onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
})

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
})
</script>

<template>
  <div id="app" class="app-container">
    <!-- 粒子背景 -->
    <ParticleBackground />

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 顶部标题栏 -->
      <header class="app-header">
        <div class="header-left">
          <h1 class="app-title">电磁脉冲实验过程展示系统</h1>
          <div class="subtitle">Electromagnetic Pulse Experiment Display System</div>
        </div>
        <div class="header-right">
          <button @click="toggleFullscreen" class="fullscreen-btn">
            <span v-if="!isFullscreen">⛶</span>
            <span v-else>⛷</span>
          </button>
        </div>
      </header>

      <!-- 实验仪表板 -->
      <ExperimentDashboard />
    </div>
  </div>
</template>

<style scoped>
.app-container {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0A192E 0%, #112240 50%, #0A192E 100%);
  overflow: hidden;
  position: relative;
}

.main-content {
  position: relative;
  z-index: 10;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: rgba(10, 25, 46, 0.9);
  border-bottom: 2px solid #00F0FF;
  box-shadow: 0 2px 20px rgba(0, 240, 255, 0.3);
}

.header-left {
  display: flex;
  flex-direction: column;
}

.app-title {
  font-size: 28px;
  font-weight: bold;
  color: #00F0FF;
  margin: 0;
  text-shadow: 0 0 10px rgba(0, 240, 255, 0.5);
  letter-spacing: 2px;
}

.subtitle {
  font-size: 14px;
  color: #39FF14;
  margin-top: 5px;
  letter-spacing: 1px;
}

.header-right {
  display: flex;
  align-items: center;
}

.fullscreen-btn {
  background: linear-gradient(45deg, #00F0FF, #39FF14);
  border: none;
  color: #0A192E;
  font-size: 20px;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 0 15px rgba(0, 240, 255, 0.4);
}

.fullscreen-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 0 25px rgba(0, 240, 255, 0.6);
}
</style>
