<template>
  <div class="field-strength-display">
    <div class="display-header">
      <h3 class="display-title">实时电场强度监测</h3>
      <div class="channel-controls">
        <button 
          v-for="channel in channels" 
          :key="channel.channel"
          @click="toggleChannel(channel.channel)"
          class="channel-btn"
          :class="{ active: channel.visible }"
          :style="{ borderColor: channel.color }"
        >
          {{ channel.channel }}
        </button>
      </div>
    </div>
    
    <div class="field-content">
      <!-- 多通道数值显示 -->
      <div class="field-values">
        <div 
          v-for="channel in visibleChannels" 
          :key="channel.channel"
          class="value-item"
          :style="{ borderColor: channel.color }"
        >
          <div class="value-header">
            <span class="channel-name">{{ channel.channel }}</span>
            <span class="channel-status" :style="{ color: channel.color }">●</span>
          </div>
          <div class="value-display">
            <span class="value-number" :style="{ color: channel.color }">
              {{ formatFieldValue(channel.currentValue) }}
            </span>
            <span class="value-unit">MV/m</span>
          </div>
          <div class="value-stats">
            <span class="stat-item">
              峰值: {{ formatFieldValue(channel.peakValue) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 电场强度图表 -->
      <div class="field-chart" ref="chartContainer"></div>
    </div>
    
    <!-- 电场统计信息 -->
    <div class="field-statistics">
      <div class="stat-group">
        <div class="stat-title">电场统计</div>
        <div class="stat-items">
          <div class="stat-item">
            <span class="stat-label">平均强度:</span>
            <span class="stat-value">{{ averageFieldStrength }} MV/m</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最大强度:</span>
            <span class="stat-value">{{ maxFieldStrength }} MV/m</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">活跃通道:</span>
            <span class="stat-value">{{ activeChannelCount }}/{{ totalChannels }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'

// Props
const props = defineProps({
  fieldData: {
    type: Array,
    default: () => []
  }
})

// 响应式数据
const chartContainer = ref(null)
const channels = ref([])
let chartInstance = null

// 颜色配置
const channelColors = ['#00F0FF', '#39FF14', '#FFC400', '#FF4444', '#7C4DFF', '#18FFFF', '#E91E63', '#FF6B35']

// 计算属性
const visibleChannels = computed(() => {
  return channels.value.filter(channel => channel.visible)
})

const totalChannels = computed(() => {
  return channels.value.length
})

const activeChannelCount = computed(() => {
  return channels.value.filter(channel => channel.currentValue > 1000).length
})

const averageFieldStrength = computed(() => {
  if (visibleChannels.value.length === 0) return '0.0'
  const sum = visibleChannels.value.reduce((acc, channel) => acc + channel.currentValue, 0)
  const avg = sum / visibleChannels.value.length / 1000000 // 转换为MV/m
  return avg.toFixed(2)
})

const maxFieldStrength = computed(() => {
  if (visibleChannels.value.length === 0) return '0.0'
  const max = Math.max(...visibleChannels.value.map(channel => channel.currentValue))
  return (max / 1000000).toFixed(2) // 转换为MV/m
})

// 方法
const formatFieldValue = (value) => {
  return (value / 1000000).toFixed(2) // 转换为MV/m
}

const toggleChannel = (channelName) => {
  const channel = channels.value.find(c => c.channel === channelName)
  if (channel) {
    channel.visible = !channel.visible
    updateChart()
  }
}

const processFieldData = () => {
  if (!props.fieldData || props.fieldData.length === 0) return
  
  channels.value = props.fieldData.map((channelData, index) => {
    const data = channelData.data || []
    const currentValue = data.length > 0 ? parseFloat(data[data.length - 1]?.value || 0) : 0
    const peakValue = data.length > 0 ? Math.max(...data.map(item => parseFloat(item.value))) : 0
    
    return {
      channel: channelData.channel,
      data: data,
      currentValue: currentValue,
      peakValue: peakValue,
      color: channelColors[index % channelColors.length],
      visible: true
    }
  })
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    legend: {
      show: false
    },
    grid: {
      left: '8%',
      right: '8%',
      top: '10%',
      bottom: '15%'
    },
    xAxis: {
      type: 'value',
      name: '时间(s)',
      nameTextStyle: { color: '#00F0FF', fontSize: 12 },
      axisLine: { lineStyle: { color: '#00F0FF60' } },
      axisTick: { lineStyle: { color: '#00F0FF60' } },
      axisLabel: { color: '#00F0FFCC', fontSize: 10 },
      splitLine: { lineStyle: { color: '#00F0FF20' } }
    },
    yAxis: {
      type: 'value',
      name: '电场强度(MV/m)',
      nameTextStyle: { color: '#00F0FF', fontSize: 12 },
      axisLine: { lineStyle: { color: '#00F0FF60' } },
      axisTick: { lineStyle: { color: '#00F0FF60' } },
      axisLabel: { 
        color: '#00F0FFCC', 
        fontSize: 10,
        formatter: (value) => (value / 1000000).toFixed(1)
      },
      splitLine: { lineStyle: { color: '#00F0FF20' } }
    },
    series: [],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(10, 25, 46, 0.9)',
      borderColor: '#00F0FF',
      textStyle: { color: '#fff' },
      formatter: (params) => {
        let content = '<div style="padding: 5px;">'
        content += '<div style="color: #00F0FF; font-weight: bold; margin-bottom: 5px;">电场强度</div>'
        params.forEach(param => {
          const channel = channels.value.find(c => c.channel === param.seriesName)
          if (channel) {
            content += `<div style="color: ${channel.color};">`
            content += `${param.seriesName}: ${formatFieldValue(param.data[1])} MV/m`
            content += '</div>'
          }
        })
        content += '</div>'
        return content
      }
    }
  }
  
  chartInstance.setOption(option)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance) return
  
  const series = visibleChannels.value.map(channel => ({
    name: channel.channel,
    type: 'line',
    data: channel.data.map(item => [parseFloat(item.time), parseFloat(item.value)]),
    smooth: true,
    lineStyle: {
      color: channel.color,
      width: 2,
      shadowColor: channel.color,
      shadowBlur: 8
    },
    itemStyle: {
      color: channel.color
    },
    symbol: 'none',
    animation: true
  }))
  
  chartInstance.setOption({
    series: series
  })
}

// 监听数据变化
watch(() => props.fieldData, () => {
  processFieldData()
  updateChart()
}, { deep: true })

// 生命周期
onMounted(() => {
  processFieldData()
  nextTick(() => {
    initChart()
    updateChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.field-strength-display {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(10, 25, 46, 0.8);
  border: 1px solid #00F0FF;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 0 20px rgba(0, 240, 255, 0.2);
}

.display-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 240, 255, 0.3);
}

.display-title {
  font-size: 18px;
  font-weight: bold;
  color: #00F0FF;
  margin: 0;
}

.channel-controls {
  display: flex;
  gap: 8px;
}

.channel-btn {
  padding: 6px 12px;
  border: 1px solid #666;
  background: transparent;
  color: #ccc;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.channel-btn.active {
  background: rgba(0, 240, 255, 0.1);
  color: #fff;
  box-shadow: 0 0 10px rgba(0, 240, 255, 0.3);
}

.channel-btn:hover {
  background: rgba(0, 240, 255, 0.05);
}

.field-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.field-values {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.value-item {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid;
  border-radius: 8px;
  padding: 15px;
  transition: all 0.3s ease;
}

.value-item:hover {
  background: rgba(0, 0, 0, 0.5);
  transform: translateY(-2px);
}

.value-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.channel-name {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
}

.channel-status {
  font-size: 16px;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.value-display {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 8px;
}

.value-number {
  font-size: 24px;
  font-weight: bold;
  font-family: 'Courier New', monospace;
  text-shadow: 0 0 10px currentColor;
}

.value-unit {
  font-size: 12px;
  color: #ccc;
}

.value-stats {
  font-size: 11px;
  color: #ccc;
}

.field-chart {
  flex: 1;
  min-height: 300px;
}

.field-statistics {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 240, 255, 0.3);
}

.stat-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.stat-title {
  font-size: 14px;
  font-weight: bold;
  color: #39FF14;
}

.stat-items {
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stat-label {
  font-size: 12px;
  color: #ccc;
}

.stat-value {
  font-size: 12px;
  font-weight: bold;
  color: #18FFFF;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .field-values {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .stat-items {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 768px) {
  .display-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .channel-controls {
    flex-wrap: wrap;
  }
}
</style>
