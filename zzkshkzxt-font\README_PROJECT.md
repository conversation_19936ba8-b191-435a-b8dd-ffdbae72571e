# 电磁脉冲实验过程展示系统

## 项目概述

这是一个基于Vue 3的现代科技风格大屏展示系统，用于展示电磁脉冲实验的完整过程和数据分析。

## 功能特性

### 🎯 实验阶段管理
- **准备阶段** (20s): 实验装备介绍和初始状态
- **一级充能阶段** (15s): 一级储能电容组充电过程
- **中储电容充能阶段** (10s): 中储电容组充电过程  
- **风化电容充能阶段** (8s): 风化电容组充电过程
- **电场建立阶段**: 电场强度监测和数据报告

### 📊 数据可视化
- **实时波形图表**: 电压、电流、气压等多通道数据
- **动态数值显示**: 格式化的实时数据展示
- **脉冲波形分析**: 开关脉冲的详细波形
- **电场强度监测**: 多通道电场强度实时监测
- **Marx建立波形**: 多通道可选择的Marx波形

### 🎮 交互控制
- **播放控制**: 开始、暂停、重置实验过程
- **阶段切换**: 手动切换到任意实验阶段
- **进度显示**: 实时显示当前阶段进度
- **全屏模式**: 支持全屏显示

### 🎨 视觉效果
- **科技风格**: 荧光蓝、绿、青、紫色主题
- **粒子背景**: 动态粒子和流动线条效果
- **发光动画**: 边框发光、脉冲动画等
- **响应式布局**: 适配不同屏幕尺寸

## 技术栈

- **前端框架**: Vue 3 (Composition API)
- **构建工具**: Vite
- **图表库**: ECharts
- **动画效果**: CSS3 + 粒子系统
- **样式**: 原生CSS + 科技风格设计

## 项目结构

```
src/
├── components/          # Vue组件
│   ├── ExperimentDashboard.vue    # 主仪表板
│   ├── ControlPanel.vue           # 控制面板
│   ├── WaveformChart.vue          # 波形图表
│   ├── VideoPlayer.vue            # 视频播放器
│   ├── PressureDisplay.vue        # 气压显示
│   ├── PulseWaveform.vue          # 脉冲波形
│   ├── FieldStrengthDisplay.vue   # 电场强度显示
│   ├── ReportSummary.vue          # 报告摘要
│   ├── MarxWaveform.vue           # Marx波形
│   └── ParticleBackground.vue     # 粒子背景
├── config/              # 配置文件
│   └── experimentConfig.js        # 实验配置
├── data/                # 数据文件
│   └── mockData.js                # Mock数据生成器
├── utils/               # 工具函数
│   └── formatters.js              # 数据格式化工具
└── assets/              # 静态资源
    └── videos/                    # 视频文件目录
```

## 安装和运行

### 环境要求
- Node.js >= 16
- npm >= 8

### 安装依赖
```bash
cd zzkshkzxt-font
npm install
```

### 开发模式
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 配置说明

### 实验阶段配置
在 `src/config/experimentConfig.js` 中可以配置：
- 各阶段持续时间
- 视频文件路径
- 颜色主题
- 电容组参数

### 数据配置
在 `src/data/mockData.js` 中可以配置：
- 波形数据生成算法
- 数据点密度
- 噪声水平
- 数值范围

## 使用说明

### 基本操作
1. **开始实验**: 点击"开始"按钮启动自动播放
2. **暂停/继续**: 点击"暂停"按钮暂停当前阶段
3. **重置**: 点击"重置"按钮回到准备阶段
4. **手动切换**: 点击阶段指示器手动切换阶段
5. **全屏**: 点击右上角全屏按钮

### 数据查看
- **悬停查看**: 鼠标悬停在图表上查看详细数据
- **通道切换**: 在多通道显示中可以切换显示的通道
- **数据导出**: 在报告阶段可以导出实验数据

## 自定义开发

### 添加新的实验阶段
1. 在 `experimentConfig.js` 中添加新阶段配置
2. 在 `mockData.js` 中添加对应的数据生成逻辑
3. 更新相关组件的显示逻辑

### 修改视觉样式
1. 在 `experimentConfig.js` 中修改颜色配置
2. 在各组件的 `<style>` 部分修改具体样式
3. 在 `main.css` 中修改全局样式

### 添加新的数据源
1. 创建新的数据获取函数
2. 在相应组件中集成数据
3. 更新图表配置以显示新数据

## 注意事项

- 确保浏览器支持现代CSS特性
- 建议使用Chrome或Firefox浏览器以获得最佳体验
- 大屏显示建议分辨率1920x1080或更高
- 视频文件需要放置在 `public/videos/` 目录下

## 许可证

本项目仅供学习和演示使用。
