<template>
  <div class="waveform-chart">
    <div class="chart-header">
      <h3 class="chart-title">{{ title }}</h3>
      <div class="chart-value">
        <span class="current-value">{{ formattedCurrentValue }}</span>
        <span class="max-value">/ {{ formattedMaxValue }}</span>
      </div>
    </div>
    
    <div class="chart-container" ref="chartContainer">
      <div v-if="!data || data.length === 0" class="no-data">
        <div class="no-data-icon">📊</div>
        <div class="no-data-text">暂无数据</div>
      </div>
    </div>
    
    <div class="chart-footer">
      <div class="chart-stats">
        <span class="stat-item">
          <span class="stat-label">峰值:</span>
          <span class="stat-value">{{ formattedPeakValue }}</span>
        </span>
        <span class="stat-item">
          <span class="stat-label">平均:</span>
          <span class="stat-value">{{ formattedAvgValue }}</span>
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import * as echarts from 'echarts'
import { formatVoltage, formatCurrent } from '../utils/formatters.js'

// Props
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    default: () => []
  },
  color: {
    type: String,
    default: '#00F0FF'
  },
  unit: {
    type: String,
    default: 'V'
  },
  maxValue: {
    type: Number,
    default: 1000
  }
})

// 响应式引用
const chartContainer = ref(null)
let chartInstance = null

// 计算属性
const currentValue = computed(() => {
  if (!props.data || props.data.length === 0) return 0
  return parseFloat(props.data[props.data.length - 1]?.value || 0)
})

const peakValue = computed(() => {
  if (!props.data || props.data.length === 0) return 0
  return Math.max(...props.data.map(item => parseFloat(item.value)))
})

const avgValue = computed(() => {
  if (!props.data || props.data.length === 0) return 0
  const sum = props.data.reduce((acc, item) => acc + parseFloat(item.value), 0)
  return sum / props.data.length
})

const formattedCurrentValue = computed(() => {
  return formatValue(currentValue.value)
})

const formattedMaxValue = computed(() => {
  return formatValue(props.maxValue)
})

const formattedPeakValue = computed(() => {
  return formatValue(peakValue.value)
})

const formattedAvgValue = computed(() => {
  return formatValue(avgValue.value)
})

// 格式化数值
const formatValue = (value) => {
  if (props.unit === 'V') {
    return formatVoltage(value)
  } else if (props.unit === 'A') {
    return formatCurrent(value)
  }
  return `${value.toFixed(2)} ${props.unit}`
}

// 初始化图表
const initChart = () => {
  if (!chartContainer.value) return
  
  chartInstance = echarts.init(chartContainer.value)
  
  const option = {
    backgroundColor: 'transparent',
    grid: {
      left: '10%',
      right: '10%',
      top: '10%',
      bottom: '20%',
      borderColor: props.color + '30'
    },
    xAxis: {
      type: 'value',
      name: '时间(s)',
      nameTextStyle: { 
        color: props.color,
        fontSize: 10
      },
      axisLine: { 
        lineStyle: { color: props.color + '60' }
      },
      axisTick: { 
        lineStyle: { color: props.color + '60' }
      },
      axisLabel: { 
        color: props.color + 'CC',
        fontSize: 10
      },
      splitLine: {
        lineStyle: {
          color: props.color + '20'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: props.unit,
      nameTextStyle: { 
        color: props.color,
        fontSize: 10
      },
      axisLine: { 
        lineStyle: { color: props.color + '60' }
      },
      axisTick: { 
        lineStyle: { color: props.color + '60' }
      },
      axisLabel: { 
        color: props.color + 'CC',
        fontSize: 10,
        formatter: (value) => {
          if (props.unit === 'V') {
            if (value >= 1000000) return `${(value / 1000000).toFixed(1)}M`
            if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
          } else if (props.unit === 'A') {
            if (value >= 1000) return `${(value / 1000).toFixed(1)}K`
          }
          return value.toFixed(0)
        }
      },
      splitLine: {
        lineStyle: {
          color: props.color + '20'
        }
      }
    },
    series: [{
      type: 'line',
      data: [],
      smooth: true,
      lineStyle: {
        color: props.color,
        width: 2,
        shadowColor: props.color,
        shadowBlur: 10
      },
      itemStyle: {
        color: props.color,
        borderColor: props.color,
        borderWidth: 2
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0,
            color: props.color + '40'
          }, {
            offset: 1,
            color: props.color + '10'
          }]
        }
      },
      symbol: 'none',
      animation: true,
      animationDuration: 300
    }],
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(10, 25, 46, 0.9)',
      borderColor: props.color,
      textStyle: {
        color: '#fff'
      },
      formatter: (params) => {
        const point = params[0]
        return `
          <div style="padding: 5px;">
            <div style="color: ${props.color}; font-weight: bold;">${props.title}</div>
            <div>时间: ${point.data[0]}s</div>
            <div>数值: ${formatValue(point.data[1])}</div>
          </div>
        `
      }
    }
  }
  
  chartInstance.setOption(option)
  
  // 响应式调整
  const resizeObserver = new ResizeObserver(() => {
    chartInstance?.resize()
  })
  resizeObserver.observe(chartContainer.value)
}

// 更新图表数据
const updateChart = () => {
  if (!chartInstance || !props.data) return
  
  const chartData = props.data.map(item => [
    parseFloat(item.time),
    parseFloat(item.value)
  ])
  
  chartInstance.setOption({
    series: [{
      data: chartData
    }]
  })
}

// 监听数据变化
watch(() => props.data, () => {
  updateChart()
}, { deep: true })

watch(() => props.color, () => {
  if (chartInstance) {
    chartInstance.dispose()
    nextTick(() => {
      initChart()
      updateChart()
    })
  }
})

// 生命周期
onMounted(() => {
  nextTick(() => {
    initChart()
    updateChart()
  })
})

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>
.waveform-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 240, 255, 0.3);
}

.chart-title {
  font-size: 14px;
  font-weight: bold;
  color: #00F0FF;
  margin: 0;
  text-shadow: 0 0 5px rgba(0, 240, 255, 0.5);
}

.chart-value {
  display: flex;
  align-items: baseline;
  gap: 5px;
}

.current-value {
  font-size: 16px;
  font-weight: bold;
  color: #39FF14;
  text-shadow: 0 0 5px rgba(57, 255, 20, 0.5);
}

.max-value {
  font-size: 12px;
  color: #666;
}

.chart-container {
  flex: 1;
  min-height: 150px;
  height: 200px;
  position: relative;
}

.no-data {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #666;
}

.no-data-icon {
  font-size: 24px;
  margin-bottom: 10px;
}

.no-data-text {
  font-size: 12px;
}

.chart-footer {
  margin-top: 10px;
  padding-top: 8px;
  border-top: 1px solid rgba(0, 240, 255, 0.3);
}

.chart-stats {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.stat-label {
  font-size: 10px;
  color: #ccc;
}

.stat-value {
  font-size: 11px;
  font-weight: bold;
  color: #18FFFF;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
  
  .chart-stats {
    flex-direction: column;
    gap: 5px;
  }
}
</style>
