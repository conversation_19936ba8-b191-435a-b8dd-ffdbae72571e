import { AnimatableColor } from "../AnimatableColor.js";
import { Collisions } from "./Collisions/Collisions.js";
import type { Container } from "../../../Core/Container.js";
import { Effect } from "./Effect/Effect.js";
import type { Engine } from "../../../Core/Engine.js";
import type { IInteractivity } from "../../Interfaces/Interactivity/IInteractivity.js";
import type { IOptionLoader } from "../../Interfaces/IOptionLoader.js";
import type { IParticlesOptions } from "../../Interfaces/Particles/IParticlesOptions.js";
import { Move } from "./Move/Move.js";
import { Opacity } from "./Opacity/Opacity.js";
import { ParticlesBounce } from "./Bounce/ParticlesBounce.js";
import type { ParticlesGroups } from "../../../Types/ParticlesGroups.js";
import { ParticlesNumber } from "./Number/ParticlesNumber.js";
import type { RecursivePartial } from "../../../Types/RecursivePartial.js";
import { Shadow } from "./Shadow.js";
import { Shape } from "./Shape/Shape.js";
import type { SingleOrMultiple } from "../../../Types/SingleOrMultiple.js";
import { Size } from "./Size/Size.js";
import { Stroke } from "./Stroke.js";
import { ZIndex } from "./ZIndex/ZIndex.js";
export declare class ParticlesOptions implements IParticlesOptions, IOptionLoader<IParticlesOptions> {
    [name: string]: unknown;
    readonly bounce: ParticlesBounce;
    readonly collisions: Collisions;
    color: AnimatableColor;
    readonly effect: Effect;
    readonly groups: ParticlesGroups;
    interactivity?: RecursivePartial<IInteractivity>;
    readonly move: Move;
    readonly number: ParticlesNumber;
    readonly opacity: Opacity;
    reduceDuplicates: boolean;
    readonly shadow: Shadow;
    readonly shape: Shape;
    readonly size: Size;
    stroke: SingleOrMultiple<Stroke>;
    readonly zIndex: ZIndex;
    private readonly _container;
    private readonly _engine;
    constructor(engine: Engine, container?: Container);
    load(data?: RecursivePartial<IParticlesOptions>): void;
}
