@import './base.css';

/* 全局样式重置和科技风格 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Arial', 'Microsoft YaHei', sans-serif;
  background: #0A192E;
  color: #fff;
  overflow: hidden;
}

#app {
  height: 100vh;
  width: 100vw;
}

/* 科技风格滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(10, 25, 46, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00F0FF, #39FF14);
  border-radius: 4px;
  box-shadow: 0 0 5px rgba(0, 240, 255, 0.3);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #39FF14, #00F0FF);
  box-shadow: 0 0 8px rgba(0, 240, 255, 0.5);
}

/* 选择文本样式 */
::selection {
  background: rgba(0, 240, 255, 0.3);
  color: #fff;
}

::-moz-selection {
  background: rgba(0, 240, 255, 0.3);
  color: #fff;
}

/* 通用动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-100%);
}

.slide-leave-to {
  transform: translateX(100%);
}

/* 科技感发光效果 */
.glow {
  box-shadow: 0 0 20px currentColor;
}

.glow-hover:hover {
  box-shadow: 0 0 30px currentColor;
  transform: translateY(-2px);
}

/* 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

.pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* 闪烁动画 */
@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

.blink {
  animation: blink 1s ease-in-out infinite;
}

/* 旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.spin {
  animation: spin 1s linear infinite;
}

/* 响应式工具类 */
.hidden-mobile {
  display: block;
}

.hidden-desktop {
  display: none;
}

@media (max-width: 768px) {
  .hidden-mobile {
    display: none;
  }

  .hidden-desktop {
    display: block;
  }
}

/* 文本工具类 */
.text-glow {
  text-shadow: 0 0 10px currentColor;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* 间距工具类 */
.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

/* Flex工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 颜色工具类 */
.text-primary { color: #00F0FF; }
.text-secondary { color: #39FF14; }
.text-accent { color: #18FFFF; }
.text-warning { color: #FFC400; }
.text-danger { color: #FF4444; }
.text-purple { color: #7C4DFF; }

.bg-primary { background-color: #00F0FF; }
.bg-secondary { background-color: #39FF14; }
.bg-accent { background-color: #18FFFF; }
.bg-warning { background-color: #FFC400; }
.bg-danger { background-color: #FF4444; }
.bg-purple { background-color: #7C4DFF; }

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}
